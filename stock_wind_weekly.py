#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WindPy API 周线数据获取脚本
用于获取各类高质量的金融周线数据

使用 w.wsd() 函数获取周线数据,通过设置 options 参数中的 Period=W 来指定周线数据
"""

from WindPy import w
import pandas as pd
from datetime import datetime, timedelta
import sys
import pymysql
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import os
import time
import warnings
import pathlib
from contextlib import contextmanager
from sqlalchemy.pool import QueuePool


# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 13306,
    'user': 'root',
    'password': '31490600',
    'database': 'instockdb',
    'charset': 'utf8mb4'
}

# 处理配置
PROCESS_CONFIG = {
    'show_progress': True,   # 是否显示进度信息
    'max_retries': 3,       # 最大重试次数
    'retry_delay': 5,       # 初始重试延迟(秒)
    'api_interval': 0.01,    # API调用间隔(秒)
    'check_connection': False,  # 是否每次检查API连接
    'connection_wait': 0.001,  # 连接等待时间(秒)

    # --- 数据获取参数配置 ---
    'fields_to_fetch': ["open", "high", "low", "close", "volume"], # 要获取的指标
    'options': "PriceAdj=F;Period=W",    # WSD选项, F:前复权, Period=W:周线数据
    'begin_date': "2025-08-01", # 数据开始日期
    'end_date': datetime.now().strftime("%Y-%m-%d") # 数据结束日期
}

# 数据库连接池配置
DB_POOL_SIZE = 5
DB_MAX_OVERFLOW = 10
DB_POOL_TIMEOUT = 30

# 全局数据库引擎
_DB_ENGINE = None

# 简单的日志函数
def log_progress(message):
    """记录进度日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] {message}\n")

def log_error(message):
    """记录错误日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] ERROR: {message}")
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(f"[{timestamp}] ERROR: {message}\n")


def start_wind_api(wait_time=120):
    """
    启动WindPy API
    
    参数:
        wait_time (int): 超时时间,默认120秒
        
    返回:
        bool: 启动是否成功
    """
    log_progress("正在启动WindPy API...")
    try:
        # 尝试导入WindPy
        from WindPy import w
        log_progress("WindPy模块导入成功")
        
        # 启动API
        result = w.start(waitTime=wait_time)
        log_progress(f"WindPy启动返回值: {result}")
        
        # WindPy返回值是一个对象,需要检查ErrorCode属性
        if hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
            log_error("WindPy API启动失败")
            log_error("\n可能的解决方案:")
            log_error("1. 确保已安装Wind终端")
            log_error("2. 确保Wind终端正在运行")
            log_error("3. 确保已正确登录Wind终端")
            log_error("4. 确保已安装WindPy插件")
            log_error("5. 尝试增加wait_time参数值")
            return False
        
        if not w.isconnected():
            log_error("WindPy未连接成功")
            return False
            
        log_progress("WindPy API启动成功")
        return True
    except ImportError:
        log_error("错误: 无法导入WindPy模块")
        log_error("\n可能的解决方案:")
        log_error("1. 确保已安装WindPy插件")
        log_error("2. 检查Python环境变量设置")
        log_error("3. 尝试重新安装WindPy插件")
        return False
    except Exception as e:
        log_error(f"WindPy API启动失败,错误信息: {str(e)}")
        log_error("\n可能的解决方案:")
        log_error("1. 重启Wind终端")
        log_error("2. 检查网络连接")
        log_error("3. 联系Wind技术支持")
        return False

def stop_wind_api():
    """停止WindPy API"""
    log_progress("正在停止WindPy API...")
    w.stop()
    log_progress("WindPy API已停止")

def get_db_engine(config=None):
    """
    获取SQLAlchemy数据库引擎(使用连接池)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        Engine: SQLAlchemy引擎对象
    """
    global _DB_ENGINE
    
    if config is None:
        # 使用全局数据库配置
        config = DB_CONFIG.copy()
    
    # 如果已存在引擎且状态正常,直接返回
    if _DB_ENGINE is not None:
        try:
            # 测试连接
            with _DB_ENGINE.connect() as conn:
                conn.execute(text("SELECT 1"))
            return _DB_ENGINE
        except Exception:
            # 连接失败,需要重新创建
            _DB_ENGINE = None
    
    # 创建新的数据库引擎
    try:
        from sqlalchemy.engine.url import URL
        db_url = URL.create(
            drivername="mysql+pymysql",
            username=config['user'],
            password=config['password'],
            host=config['host'],
            port=config['port'],
            database=config['database']
        )
        
        _DB_ENGINE = create_engine(
            db_url,
            poolclass=QueuePool,
            pool_size=DB_POOL_SIZE,
            max_overflow=DB_MAX_OVERFLOW,
            pool_timeout=DB_POOL_TIMEOUT,
            pool_recycle=3600  # 1小时后回收连接
        )
        
        # 测试连接
        with _DB_ENGINE.connect() as conn:
            conn.execute(text("SELECT 1"))
            
        log_progress("MySQL数据库引擎创建成功(使用连接池)")
        return _DB_ENGINE
        
    except Exception as e:
        log_error(f"创建数据库引擎失败: {str(e)}")
        return None

@contextmanager
def db_connection(config=None):
    """
    数据库连接上下文管理器
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    使用方法:
        with db_connection() as conn:
            # 使用连接执行操作
            pass
    """
    engine = get_db_engine(config)
    if engine is None:
        log_error("无法获取数据库引擎")
        yield None
        return
        
    connection = None
    try:
        # 从连接池获取连接
        connection = engine.connect()
        yield connection
    except Exception as e:
        log_error(f"数据库连接失败: {str(e)}")
        yield None
    finally:
        if connection:
            connection.close()

def get_mysql_connection(config=None):
    """
    获取MySQL数据库连接(兼容旧接口)
    
    参数:
        config (dict): 数据库配置,如果为None则使用默认配置
        
    返回:
        connection: 数据库连接对象
    """
    engine = get_db_engine(config)
    if engine is None:
        return None
        
    try:
        # 使用engine.raw_connection()获取原始DBAPI连接
        raw_conn = engine.raw_connection()
        return raw_conn
    except Exception as e:
        log_error(f"MySQL数据库连接失败: {str(e)}")
        return None

class MySQLConnectionWrapper:
    """
    MySQL连接包装器,将SQLAlchemy连接包装为类似pymysql的连接
    """
    def __init__(self, sqlalchemy_conn):
        self._conn = sqlalchemy_conn
        self._cursor = None
        
    def cursor(self):
        """创建游标"""
        if self._cursor is None:
            # 使用SQLAlchemy的engine.raw_connection()获取底层DBAPI连接
            self._cursor = self._conn.engine.raw_connection().cursor()
        return self._cursor
        
    def commit(self):
        """提交事务"""
        self._conn.commit()
        
    def rollback(self):
        """回滚事务"""
        self._conn.rollback()
        
    def close(self):
        """关闭连接"""
        if self._cursor:
            self._cursor.close()
            self._cursor = None
        self._conn.close()
        
    @property
    def user(self):
        return self._conn.engine.url.username
        
    @property
    def password(self):
        return self._conn.engine.url.password
        
    @property
    def host(self):
        return self._conn.engine.url.host
        
    @property
    def port(self):
        return self._conn.engine.url.port
        
    @property
    def db(self):
        return self._conn.engine.url.database
        
    def get_host_info(self):
        """获取主机信息(兼容pymysql接口)"""
        return f"{self.host} via TCP/IP"

def get_stock_codes_from_wind():
    """
    通过Wind API获取所有A股代码
    :return: 包含股票代码和名称的DataFrame
    """
    log_progress("正在通过Wind API获取所有A股代码...")
    # sectorid=a001010100000000 代表全部A股
    today_str = datetime.now().strftime("%Y-%m-%d")
    params = f"date={today_str};sectorid=a001010100000000"
    result = retry_api_call(w.wset, "sectorconstituent", params)

    if result is not None and result.Data and len(result.Data) >= 2:
        # Wind API sectorconstituent 返回的数据结构通常是:
        # Data[0]: 日期列表 (所有记录的日期都相同)
        # Data[1]: 股票代码列表
        # Data[2]: 股票名称列表 (如果有的话)

        # 添加调试信息:显示原始数据样本和结构
        log_progress(f"Wind API返回数据结构: Data长度={len(result.Data)}")
        for i, data_list in enumerate(result.Data):
            sample_data = data_list[:3] if len(data_list) > 3 else data_list
            log_progress(f"Data[{i}] 样本(前3个): {sample_data}")
            log_progress(f"Data[{i}] 数据类型: {type(data_list[0]) if data_list else 'Empty'}")

        # 根据实际数据结构解析
        if len(result.Data) >= 3:
            # 有3列数据:日期、代码、名称
            dates = result.Data[0]
            codes = result.Data[1]
            names = result.Data[2]

            # 验证数据格式
            if codes and isinstance(codes[0], str) and '.' in codes[0]:
                log_progress("识别到标准格式: [日期, 股票代码, 股票名称]")
                df = pd.DataFrame({'ts_code': codes, 'name': names})
            else:
                log_error("数据格式不符合预期")
                return None

        elif len(result.Data) >= 2:
            # 只有2列数据,需要判断哪列是代码
            data1 = result.Data[0]
            data2 = result.Data[1]

            if data1 and isinstance(data1[0], str) and '.' in data1[0]:
                # data1是股票代码
                log_progress("识别到格式: [股票代码, 其他数据]")
                df = pd.DataFrame({'ts_code': data1, 'name': data2})
            elif data2 and isinstance(data2[0], str) and '.' in data2[0]:
                # data2是股票代码
                log_progress("识别到格式: [其他数据, 股票代码]")
                df = pd.DataFrame({'ts_code': data2, 'name': data1})
            else:
                log_error("无法识别股票代码列")
                return None
        else:
            log_error("Wind API返回的数据列数不足")
            return None

        # 添加调试信息:显示去重前的数据样本
        log_progress(f"去重前数据样本:")
        log_progress(f"{df.head(10).to_string()}")

        # 在DataFrame层面进行去重,防止'repeated windcodes'错误
        original_count = len(df)

        # 检查是否有重复的股票代码
        duplicates = df[df.duplicated(subset=['ts_code'], keep=False)]
        if not duplicates.empty:
            log_progress(f"发现重复的股票代码:")
            log_progress(f"{duplicates.to_string()}")

        df.drop_duplicates(subset=['ts_code'], inplace=True, keep='first')

        # 添加调试信息:显示去重后的数据样本
        log_progress(f"去重后数据样本:")
        log_progress(f"{df.head(10).to_string()}")

        if len(df) < original_count:
            log_progress(f"从Wind API获取到 {original_count} 条记录, 去重后剩余 {len(df)} 条")
        else:
            log_progress(f"从Wind API获取到 {len(df)} 只股票信息")

        return df
    else:
        log_error("从Wind API获取股票代码失败, API返回的数据为空或格式不正确.")
        if result is not None:
            log_error(f"API返回结果: ErrorCode={getattr(result, 'ErrorCode', 'N/A')}")
            log_error(f"API返回数据: {getattr(result, 'Data', 'N/A')}")
        return None

def get_wind_code_from_ts_code(ts_code):
    """
    将ts_code转换为Wind代码格式
    
    参数:
        ts_code (str): ts代码格式,如'000001.SZ'
        
    返回:
        str: Wind代码格式,如'000001.SZ'(相同格式,但可能需要转换)
    """
    # ts_code和Wind代码格式可能相同,但如果有特殊转换需求可以在这里处理
    return ts_code

def get_friday_of_week(date):
    """
    获取指定日期所在周的周五日期
    
    参数:
        date (datetime): 日期对象
        
    返回:
        datetime: 周五的日期对象
    """
    # 0是周一,1是周二,2是周三,3是周四,4是周五,5是周六,6是周日
    days_to_friday = 4 - date.weekday()
    if days_to_friday < 0:
        days_to_friday += 7
    friday = date + timedelta(days=days_to_friday)
    return friday

def clear_table_data(conn, table_name):
    """
    清空表数据
    
    参数:
        conn: 数据库连接
        table_name: 表名
        
    返回:
        bool: 是否成功
    """
    try:
        # 检查连接类型并执行SQL
        if hasattr(conn, 'execute'):
            # SQLAlchemy连接
            sql = f"TRUNCATE TABLE {table_name}"
            conn.execute(text(sql))
            conn.commit()
        else:
            # 原始DBAPI连接
            cursor = conn.cursor()
            sql = f"TRUNCATE TABLE {table_name}"
            cursor.execute(sql)
            conn.commit()
            cursor.close()
            
        log_progress(f"已清空表 {table_name} 的数据")
        return True
    except Exception as e:
        log_error(f"清空表 {table_name} 数据失败: {str(e)}")
        try:
            if hasattr(conn, 'rollback'):
                conn.rollback()
        except:
            pass
        return False



def retry_api_call(func, *args, max_retries=PROCESS_CONFIG['max_retries'],
                  retry_delay=PROCESS_CONFIG['retry_delay'], **kwargs):
    """
    带重试机制的API调用
    
    参数:
        func: 要调用的函数
        *args: 函数参数
        max_retries: 最大重试次数
        retry_delay: 重试延迟(秒)
        **kwargs: 函数关键字参数
        
    返回:
        函数调用结果或None(如果所有重试都失败)
    """
    last_error = None
    for retry in range(max_retries + 1):
        try:
            result = func(*args, **kwargs)
            
            # 检查结果是否有效
            if result is not None:
                if isinstance(result, tuple) and len(result) > 0 and hasattr(result[0], 'ErrorCode'):
                    if result[0].ErrorCode != 0:
                        raise Exception(f"API返回错误代码: {result[0].ErrorCode}")
                elif hasattr(result, 'ErrorCode') and result.ErrorCode != 0:
                    raise Exception(f"API返回错误代码: {result.ErrorCode}")
                    
                # 成功获取数据
                if retry > 0:
                    log_progress(f"第{retry+1}次重试成功")
                return result
                
        except Exception as e:
            last_error = str(e)
            if retry < max_retries:
                # 指数退避策略
                delay = retry_delay * (2 ** retry)
                log_error(f"第{retry+1}次调用失败: {last_error}, {delay}秒后重试")
                time.sleep(delay)
            else:
                log_error(f"所有重试均失败: {last_error}")
                
    return None

def get_weekly_data_by_field(codes, field, begin_date, end_date, options=""):
    """
    获取所有codes在指定时间范围和单个field上的周线数据
    """
    log_progress(f"    正在获取周线 [{field}] 数据,股票数量: {len(codes)}")

    # 确保options包含周线参数
    if "Period=W" not in options:
        options = f"Period=W;{options}" if options else "Period=W"

    result = retry_api_call(w.wsd, codes, field, begin_date, end_date, options, usedf=True)

    if result is not None:
        df = result[1]
        log_progress(f"    API返回数据形状: {df.shape}")

        if not df.empty:
            # 显示原始数据样本
            log_progress(f"    原始数据样本:")
            log_progress(f"    {df.head().to_string()}")

            # 重置索引并转换为长格式
            df_reset = df.reset_index()
            log_progress(f"    重置索引后数据形状: {df_reset.shape}")
            log_progress(f"    重置索引后列名: {list(df_reset.columns)}")

            # 重命名列 - 第一列是日期,其余列是股票代码对应的数据
            new_columns = ['trade_date'] + [col for col in df_reset.columns[1:]]
            df_reset.columns = new_columns

            # 转换为长格式
            df_long = pd.melt(df_reset, id_vars=['trade_date'], var_name='ts_code', value_name=field)
            log_progress(f"    转换后数据形状: {df_long.shape}")

            # 显示转换后数据样本
            log_progress(f"    转换后数据样本:")
            log_progress(f"    {df_long.head(10).to_string()}")

            # 过滤前记录数
            before_filter = len(df_long)

            # 过滤掉API可能返回的错误信息行
            df_filtered = df_long[df_long['ts_code'].str.contains('.', na=False)]

            # 过滤后记录数
            after_filter = len(df_filtered)
            log_progress(f"    过滤前记录数: {before_filter}, 过滤后记录数: {after_filter}")

            if after_filter < before_filter:
                # 显示被过滤掉的数据
                filtered_out = df_long[~df_long['ts_code'].str.contains('.', na=False)]
                log_progress(f"    被过滤掉的数据:")
                log_progress(f"    {filtered_out.to_string()}")

            log_progress(f"    最终返回数据形状: {df_filtered.shape}")
            return df_filtered

    log_error(f"获取周线 [{field}] 数据失败")
    return None



def process_all_weekly_stocks():
    """获取所有股票代码的周线数据并写入数据库"""
    log_progress("===== 开始获取所有股票代码的周线数据并写入数据库 =====")

    # 通过Wind API获取股票代码
    stocks_df = get_stock_codes_from_wind()

    if stocks_df is None or len(stocks_df) == 0:
        log_error("无法获取股票代码")
        return

    # 清空表数据
    log_progress("正在清空表 ts_stock_weekly 的数据...")
    with db_connection() as db_conn:
        if db_conn is None:
            log_error("无法连接到数据库,请检查数据库配置")
            return

        if not clear_table_data(db_conn, 'ts_stock_weekly'):
            log_error("清空表数据失败,处理终止")
            return

    all_codes = stocks_df['ts_code'].tolist()
    fields_to_fetch = PROCESS_CONFIG['fields_to_fetch']
    options = PROCESS_CONFIG['options']
    begin_date = PROCESS_CONFIG['begin_date']
    end_date = PROCESS_CONFIG['end_date']

    log_progress(f"数据获取时间范围: {begin_date} 至 {end_date}")
    log_progress(f"获取指标: {fields_to_fetch}")
    log_progress(f"WSD选项: {options}")

    # 获取周线数据 - 一次性获取所有股票的所有指标
    log_progress(" -> 正在获取周线数据")

    all_field_dfs = []
    # 按指标循环
    for field in fields_to_fetch:
        df_field = get_weekly_data_by_field(all_codes, field, begin_date, end_date, options)
        if df_field is not None and not df_field.empty:
            log_progress(f"    成功获取 {field} 数据: {len(df_field)} 条记录")
            all_field_dfs.append(df_field)
        else:
            log_progress(f"    获取 {field} 数据失败或为空")

    # 合并所有指标的数据
    if len(all_field_dfs) > 0:
        log_progress(f"  正在合并 {len(all_field_dfs)} 个指标的数据...")
        from functools import reduce
        merged_df = reduce(lambda left, right: pd.merge(left, right, on=['ts_code', 'trade_date'], how='outer'), all_field_dfs)
        log_progress(f"  合并后数据形状: {merged_df.shape}")
        log_progress(f"  合并后数据样本:")
        log_progress(f"  {merged_df.head().to_string()}")
    else:
        log_progress(f"  没有获取到任何有效数据")
        return

    # 合并数据
    log_progress("正在合并数据...")
    final_df = merged_df.copy()
    log_progress(f"合并后总数据形状: {final_df.shape}")
    log_progress(f"合并后数据列: {list(final_df.columns)}")
    log_progress(f"合并后数据样本:")
    log_progress(f"{final_df.head().to_string()}")

    # 创建字段名映射,将Wind API字段名映射到数据库列名
    field_mapping = {
        'open': 'open',
        'high': 'high',
        'low': 'low',
        'close': 'close',
        'volume': 'vol'  # volume -> vol
    }

    # 重命名列
    rename_dict = {field: field_mapping.get(field, field) for field in fields_to_fetch}
    final_df.rename(columns=rename_dict, inplace=True)
    log_progress(f"字段映射: {rename_dict}")
    log_progress(f"重命名后数据列: {list(final_df.columns)}")

    log_progress("正在补充股票信息并整理格式...")
    stock_info_map = pd.Series(stocks_df.name.values, index=stocks_df.ts_code).to_dict()
    log_progress(f"股票信息映射数量: {len(stock_info_map)}")

    final_df['name'] = final_df['ts_code'].map(stock_info_map)
    final_df['stock_code'] = final_df['ts_code'].str.split('.').str[0]

    db_columns = [
        'ts_code', 'stock_code', 'name', 'trade_date', 'open', 'high', 'low',
        'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount', 'adj_factor'
    ]

    for col in db_columns:
        if col not in final_df.columns:
            final_df[col] = None

    final_df = final_df[db_columns]
    log_progress(f"整理后数据形状: {final_df.shape}")

    # 显示过滤前的数据统计
    log_progress(f"过滤前数据统计:")
    log_progress(f"  总记录数: {len(final_df)}")
    log_progress(f"  open列非空数: {final_df['open'].notna().sum()}")
    log_progress(f"  high列非空数: {final_df['high'].notna().sum()}")
    log_progress(f"  low列非空数: {final_df['low'].notna().sum()}")
    log_progress(f"  close列非空数: {final_df['close'].notna().sum()}")
    log_progress(f"  vol列非空数: {final_df['vol'].notna().sum()}")

    final_df.dropna(subset=['open', 'high', 'low', 'close', 'vol'], how='all', inplace=True)
    log_progress(f"过滤后数据形状: {final_df.shape}")

    if not final_df.empty:
        log_progress(f"最终数据样本:")
        log_progress(f"{final_df.head().to_string()}")
    else:
        log_progress("最终数据为空!")
        return

    # 数据类型转换和清理
    log_progress("正在进行数据类型转换...")

    # 确保数值列为float类型
    numeric_columns = ['open', 'high', 'low', 'close', 'vol', 'pre_close', 'change', 'pct_chg', 'amount', 'adj_factor']
    for col in numeric_columns:
        if col in final_df.columns:
            final_df[col] = pd.to_numeric(final_df[col], errors='coerce')

    # 确保日期列为datetime类型
    final_df['trade_date'] = pd.to_datetime(final_df['trade_date'], errors='coerce')

    # 确保字符串列为string类型
    string_columns = ['ts_code', 'stock_code', 'name']
    for col in string_columns:
        if col in final_df.columns:
            final_df[col] = final_df[col].astype(str)

    log_progress(f"数据类型转换完成,最终数据形状: {final_df.shape}")

    # 保存到数据库
    if not final_df.empty:
        try:
            # 使用引擎直接保存,避免连接管理问题
            engine = get_db_engine()
            if engine is None:
                log_error("无法获取数据库引擎")
                return

            # 分批保存,避免内存问题
            batch_size = 1000
            total_rows = len(final_df)

            for i in range(0, total_rows, batch_size):
                batch_df = final_df.iloc[i:i+batch_size].copy()

                batch_df.to_sql(
                    'ts_stock_weekly',
                    engine,
                    if_exists='append',
                    index=False,
                    chunksize=500
                )

                log_progress(f"已保存批次 {i//batch_size + 1}/{(total_rows-1)//batch_size + 1}")

            log_progress(f"成功向表 ts_stock_weekly 保存 {len(final_df)} 条记录")
        except Exception as e:
            log_error(f"保存数据到数据库失败: {str(e)}")
            import traceback
            log_error(f"详细错误信息: {traceback.format_exc()}")
            return
    else:
        log_progress("没有数据需要保存")

    log_progress("===== 处理完成 =====")
    log_progress(f"总共处理了 {len(stocks_df)} 只股票")
    log_progress(f"成功保存了 {len(final_df)} 条记录到数据库")


if __name__ == "__main__":
    # 启动WindPy API
    if start_wind_api():
        try:
            # 处理所有股票周线数据
            process_all_weekly_stocks()

        finally:
            # 停止WindPy API
            stop_wind_api()