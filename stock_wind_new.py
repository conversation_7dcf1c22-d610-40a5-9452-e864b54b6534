#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WindPy API 数据获取脚本 (高效批量版)
根据API特性,采用按指标批量获取所有股票数据的方式,实现最高效率.
股票列表直接从Wind API实时获取.
采用按日循环的方式,确保单次请求数据量在API限制内.
使用w.tdays获取准确的交易日历.
"""

import os
import time
import pathlib
import warnings
from datetime import datetime
from contextlib import contextmanager
from functools import reduce

import pandas as pd
from WindPy import w
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool
from sqlalchemy.engine.url import URL

# --- 配置区 ---

# 创建日志目录
log_dir = pathlib.Path(__file__).parent / 'log'
log_dir.mkdir(exist_ok=True)

# 获取脚本名称
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 数据库连接配置
DB_CONFIG = {
    'host': '*************',
    'port': 13306,
    'user': 'root',
    'password': '31490600',
    'database': 'instockdb',
    'charset': 'utf8mb4'
}

# 处理配置
PROCESS_CONFIG = {
    'show_progress': True,      # 是否显示进度信息
    'max_retries': 3,           # 最大重试次数
    'retry_delay': 5,           # 初始重试延迟(秒)
    
    # --- 数据获取参数配置 ---
    'fields_to_fetch': ["open", "high", "low", "close", "volume"], # 要获取的指标
    'options': "PriceAdj=F",    # WSD选项, F:前复权, B:后复权, 空字符串:不复权
    'begin_date': "2025-08-11", # 数据开始日期
    'end_date': datetime.now().strftime("%Y-%m-%d") # 数据结束日期
}

# 数据库连接池配置
DB_POOL_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600,       # 1小时后回收连接
    'pool_pre_ping': True       # 自动测试连接有效性
}

# 全局数据库引擎
_DB_ENGINE = None


# --- 日志模块 ---

def log_progress(message):
    """记录进度日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] {message}"
    print(log_message)
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(log_message + "\n")

def log_error(message):
    """记录错误日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] ERROR: {message}"
    print(log_message)
    with open(log_dir / f"{script_name}.log", 'a', encoding='utf-8') as f:
        f.write(log_message + "\n")


# --- WindPy API 模块 ---

def start_wind_api(wait_time=120):
    """
    启动WindPy API
    :param wait_time: 超时时间,默认120秒
    :return: 启动是否成功
    """
    log_progress("正在启动WindPy API...")
    try:
        result = w.start(waitTime=wait_time)
        if result.ErrorCode != 0:
            log_error(f"WindPy API启动失败, 错误码: {result.ErrorCode}, 信息: {result.Data[0][0]}")
            return False
        if not w.isconnected():
            log_error("WindPy未连接成功")
            return False
        log_progress("WindPy API启动成功")
        return True
    except Exception as e:
        log_error(f"WindPy API启动时发生异常: {e}")
        return False

def stop_wind_api():
    """停止WindPy API"""
    log_progress("正在停止WindPy API...")
    w.stop()
    log_progress("WindPy API已停止")


# --- 数据库模块 ---

def get_db_engine():
    """
    获取全局唯一的SQLAlchemy数据库引擎(使用连接池)
    :return: SQLAlchemy引擎对象
    """
    global _DB_ENGINE
    if _DB_ENGINE is not None:
        return _DB_ENGINE

    try:
        db_url = URL.create(
            drivername="mysql+pymysql",
            username=DB_CONFIG['user'],
            password=DB_CONFIG['password'],
            host=DB_CONFIG['host'],
            port=DB_CONFIG['port'],
            database=DB_CONFIG['database'],
            query={'charset': DB_CONFIG['charset']}
        )
        
        _DB_ENGINE = create_engine(db_url, **DB_POOL_CONFIG)
        
        # 测试连接
        with _DB_ENGINE.connect() as conn:
            conn.execute(text("SELECT 1"))
            
        log_progress("MySQL数据库引擎创建成功(使用连接池)")
        return _DB_ENGINE
    except Exception as e:
        log_error(f"创建数据库引擎失败: {e}")
        _DB_ENGINE = None
        return None

@contextmanager
def db_connection():
    """
    数据库连接上下文管理器,从连接池获取和释放连接
    """
    engine = get_db_engine()
    if engine is None:
        yield None
        return
        
    connection = None
    try:
        connection = engine.connect()
        yield connection
    except Exception as e:
        log_error(f"从连接池获取数据库连接失败: {e}")
        yield None
    finally:
        if connection:
            connection.close()


# --- 数据处理模块 ---

def get_stock_codes_from_wind():
    """
    通过Wind API获取所有A股代码
    :return: 包含股票代码和名称的DataFrame
    """
    log_progress("正在通过Wind API获取所有A股代码...")
    # sectorid=a001010100000000 代表全部A股
    today_str = datetime.now().strftime("%Y-%m-%d")
    params = f"date={today_str};sectorid=a001010100000000"
    result = retry_api_call(w.wset, "sectorconstituent", params)

    if result is not None and result.Data and len(result.Data) >= 2:
        # Wind API sectorconstituent 返回的数据结构通常是:
        # Data[0]: 日期列表 (所有记录的日期都相同)
        # Data[1]: 股票代码列表
        # Data[2]: 股票名称列表 (如果有的话)

        # 添加调试信息:显示原始数据样本和结构
        log_progress(f"Wind API返回数据结构: Data长度={len(result.Data)}")
        for i, data_list in enumerate(result.Data):
            sample_data = data_list[:3] if len(data_list) > 3 else data_list
            log_progress(f"Data[{i}] 样本(前3个): {sample_data}")
            log_progress(f"Data[{i}] 数据类型: {type(data_list[0]) if data_list else 'Empty'}")

        # 根据实际数据结构解析
        if len(result.Data) >= 3:
            # 有3列数据:日期、代码、名称
            dates = result.Data[0]
            codes = result.Data[1]
            names = result.Data[2]

            # 验证数据格式
            if codes and isinstance(codes[0], str) and '.' in codes[0]:
                log_progress("识别到标准格式: [日期, 股票代码, 股票名称]")
                df = pd.DataFrame({'ts_code': codes, 'name': names})
            else:
                log_error("数据格式不符合预期")
                return None

        elif len(result.Data) >= 2:
            # 只有2列数据,需要判断哪列是代码
            data1 = result.Data[0]
            data2 = result.Data[1]

            if data1 and isinstance(data1[0], str) and '.' in data1[0]:
                # data1是股票代码
                log_progress("识别到格式: [股票代码, 其他数据]")
                df = pd.DataFrame({'ts_code': data1, 'name': data2})
            elif data2 and isinstance(data2[0], str) and '.' in data2[0]:
                # data2是股票代码
                log_progress("识别到格式: [其他数据, 股票代码]")
                df = pd.DataFrame({'ts_code': data2, 'name': data1})
            else:
                log_error("无法识别股票代码列")
                return None
        else:
            log_error("Wind API返回的数据列数不足")
            return None

        # 添加调试信息:显示去重前的数据样本
        log_progress(f"去重前数据样本:")
        log_progress(f"{df.head(10).to_string()}")

        # 在DataFrame层面进行去重,防止'repeated windcodes'错误
        original_count = len(df)

        # 检查是否有重复的股票代码
        duplicates = df[df.duplicated(subset=['ts_code'], keep=False)]
        if not duplicates.empty:
            log_progress(f"发现重复的股票代码:")
            log_progress(f"{duplicates.to_string()}")

        df.drop_duplicates(subset=['ts_code'], inplace=True, keep='first')

        # 添加调试信息:显示去重后的数据样本
        log_progress(f"去重后数据样本:")
        log_progress(f"{df.head(10).to_string()}")

        if len(df) < original_count:
            log_progress(f"从Wind API获取到 {original_count} 条记录, 去重后剩余 {len(df)} 条")
        else:
            log_progress(f"从Wind API获取到 {len(df)} 只股票信息")

        return df
    else:
        log_error("从Wind API获取股票代码失败, API返回的数据为空或格式不正确.")
        if result is not None:
            log_error(f"API返回结果: ErrorCode={getattr(result, 'ErrorCode', 'N/A')}")
            log_error(f"API返回数据: {getattr(result, 'Data', 'N/A')}")
        return None


def retry_api_call(func, *args, **kwargs):
    """
    带重试机制的API调用
    :param func: 要调用的函数
    :param args: 函数位置参数
    :param kwargs: 函数关键字参数
    :return: 函数调用结果或None
    """
    last_error = None
    for attempt in range(PROCESS_CONFIG['max_retries'] + 1):
        try:
            if not w.isconnected():
                log_progress("检测到Wind API未连接, 尝试重新连接...")
                if not start_wind_api():
                    raise ConnectionError("Wind API重连失败")
                time.sleep(2)
            
            result = func(*args, **kwargs)
            
            error_code = 0
            error_msg = ""
            if isinstance(result, tuple) and len(result) > 0:
                if hasattr(result[0], 'ErrorCode'):
                    error_code = result[0].ErrorCode
                    error_msg = result[0].Data
            elif hasattr(result, 'ErrorCode'):
                error_code = result.ErrorCode
                error_msg = result.Data
            
            if error_code != 0:
                raise Exception(f"API返回错误码: {error_code}, 信息: {error_msg}")
            
            if attempt > 0:
                log_progress(f"在第{attempt+1}次尝试中成功")
            return result
            
        except Exception as e:
            last_error = str(e)
            if attempt < PROCESS_CONFIG['max_retries']:
                delay = PROCESS_CONFIG['retry_delay'] * (2 ** attempt)
                log_error(f"第{attempt+1}次调用失败: {last_error}, {delay}秒后重试...")
                time.sleep(delay)
            else:
                log_error(f"所有重试均失败: {last_error}")
    return None

def get_daily_data_by_field(codes, field, target_date, options=""):
    """
    获取所有codes在指定单日和单个field上的数据
    """
    log_progress(f"    正在获取 {target_date} 的 [{field}] 数据,股票数量: {len(codes)}")
    result = retry_api_call(w.wsd, codes, field, target_date, target_date, options, usedf=True)

    if result is not None:
        df = result[1]
        log_progress(f"    API返回数据形状: {df.shape}")

        if not df.empty:
            # 显示原始数据样本
            log_progress(f"    原始数据样本:")
            log_progress(f"    {df.head().to_string()}")

            # 重置索引并转换为长格式
            df_reset = df.reset_index()
            log_progress(f"    重置索引后数据形状: {df_reset.shape}")
            log_progress(f"    重置索引后列名: {list(df_reset.columns)}")

            # 重命名列
            df_reset.columns = ['ts_code', field]
            log_progress(f"    转换后数据形状: {df_reset.shape}")

            # 显示转换后数据样本
            log_progress(f"    转换后数据样本:")
            log_progress(f"    {df_reset.head(10).to_string()}")

            # 过滤前记录数
            before_filter = len(df_reset)

            # 过滤掉API可能返回的错误信息行
            df_filtered = df_reset[df_reset['ts_code'].str.contains('.', na=False)]

            # 过滤后记录数
            after_filter = len(df_filtered)
            log_progress(f"    过滤前记录数: {before_filter}, 过滤后记录数: {after_filter}")

            if after_filter < before_filter:
                # 显示被过滤掉的数据
                filtered_out = df_reset[~df_reset['ts_code'].str.contains('.', na=False)]
                log_progress(f"    被过滤掉的数据:")
                log_progress(f"    {filtered_out.to_string()}")

            df_filtered['trade_date'] = pd.to_datetime(target_date)
            log_progress(f"    最终返回数据形状: {df_filtered.shape}")
            return df_filtered

    log_error(f"获取 {target_date} 的 [{field}] 数据失败")
    return None

def save_data_to_db(df, table_name):
    """
    将DataFrame数据保存到数据库
    """
    if df is None or df.empty:
        log_progress("没有数据需要保存")
        return 0
    
    try:
        with db_connection() as conn:
            if conn is None:
                raise ConnectionError("无法建立数据库连接")
            
            df.to_sql(
                table_name,
                conn,
                if_exists='append',
                index=False,
                chunksize=5000 
            )
            log_progress(f"成功向表 {table_name} 保存 {len(df)} 条记录")
            return len(df)
    except Exception as e:
        log_error(f"保存数据到数据库表 {table_name} 失败: {e}")
        return 0

def clear_table_data(table_name):
    """
    清空指定表的数据
    """
    log_progress(f"正在清空表 {table_name} 的数据...")
    try:
        with db_connection() as conn:
            if conn is None:
                raise ConnectionError("无法建立数据库连接")
            
            with conn.begin() as trans:
                conn.execute(text(f"TRUNCATE TABLE {table_name}"))
            
        log_progress(f"已成功清空表 {table_name}")
        return True
    except Exception as e:
        log_error(f"清空表 {table_name} 数据失败: {e}")
        return False


# --- 主流程 ---

def process_all_stocks():
    """
    获取所有股票代码的数据并写入数据库 (按日循环版)
    """
    log_progress("===== 开始按日循环获取所有股票数据并写入数据库 =====")
    
    stocks_df = get_stock_codes_from_wind()
    if stocks_df is None or stocks_df.empty:
        log_error("无法从Wind API获取股票代码,处理终止")
        return

    if not clear_table_data('ts_stock_daily'):
        log_error("清空目标表失败,处理终止")
        return

    all_codes = stocks_df['ts_code'].tolist()
    fields_to_fetch = PROCESS_CONFIG['fields_to_fetch']
    options = PROCESS_CONFIG['options']
    begin_date = PROCESS_CONFIG['begin_date']
    end_date = PROCESS_CONFIG['end_date']
    
    log_progress(f"数据获取时间范围: {begin_date} 至 {end_date}")
    log_progress(f"获取指标: {fields_to_fetch}")
    log_progress(f"WSD选项: {options}")

    # 使用 w.tdays 获取准确的交易日列表
    log_progress("正在获取交易日历...")
    tdays_result = retry_api_call(w.tdays, begin_date, end_date, "")
    if tdays_result is None or not tdays_result.Times:
        log_error("获取交易日历失败,处理终止")
        return
    date_range = tdays_result.Times
    log_progress(f"获取到 {len(date_range)} 个交易日")
    
    all_days_data = []

    # 按天循环
    for single_date in date_range:
        date_str = single_date.strftime("%Y-%m-%d")
        log_progress(f" -> 正在处理日期: {date_str}")

        daily_dfs = []
        # 按指标循环
        for field in fields_to_fetch:
            df_field = get_daily_data_by_field(all_codes, field, date_str, options)
            if df_field is not None and not df_field.empty:
                log_progress(f"    成功获取 {field} 数据: {len(df_field)} 条记录")
                daily_dfs.append(df_field)
            else:
                log_progress(f"    获取 {field} 数据失败或为空")

        # 合并当天的所有指标数据
        if len(daily_dfs) > 0:
            log_progress(f"  正在合并 {len(daily_dfs)} 个指标的数据...")
            merged_day_df = reduce(lambda left, right: pd.merge(left, right, on=['ts_code', 'trade_date'], how='outer'), daily_dfs)
            log_progress(f"  合并后数据形状: {merged_day_df.shape}")
            log_progress(f"  合并后数据样本:")
            log_progress(f"  {merged_day_df.head().to_string()}")
            all_days_data.append(merged_day_df)
        else:
            log_progress(f"  {date_str} 没有获取到任何有效数据")

    if not all_days_data:
        log_error("在指定时间范围内未能获取任何数据,处理终止")
        return

    # 合并所有天的数据
    log_progress("正在合并所有日期的数据...")
    log_progress(f"需要合并的数据集数量: {len(all_days_data)}")

    final_df = pd.concat(all_days_data, ignore_index=True)
    log_progress(f"合并后总数据形状: {final_df.shape}")
    log_progress(f"合并后数据列: {list(final_df.columns)}")
    log_progress(f"合并后数据样本:")
    log_progress(f"{final_df.head().to_string()}")

    # 创建字段名映射,将Wind API字段名映射到数据库列名
    field_mapping = {
        'open': 'open',
        'high': 'high',
        'low': 'low',
        'close': 'close',
        'volume': 'vol'  # volume -> vol
    }

    # 重命名列
    rename_dict = {field: field_mapping.get(field, field) for field in fields_to_fetch}
    final_df.rename(columns=rename_dict, inplace=True)
    log_progress(f"字段映射: {rename_dict}")
    log_progress(f"重命名后数据列: {list(final_df.columns)}")

    log_progress("正在补充股票信息并整理格式...")
    stock_info_map = pd.Series(stocks_df.name.values, index=stocks_df.ts_code).to_dict()
    log_progress(f"股票信息映射数量: {len(stock_info_map)}")

    final_df['name'] = final_df['ts_code'].map(stock_info_map)
    final_df['stock_code'] = final_df['ts_code'].str.split('.').str[0]

    db_columns = [
        'ts_code', 'stock_code', 'name', 'trade_date', 'open', 'high', 'low',
        'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount', 'adj_factor'
    ]

    for col in db_columns:
        if col not in final_df.columns:
            final_df[col] = None

    final_df = final_df[db_columns]
    log_progress(f"整理后数据形状: {final_df.shape}")

    # 显示过滤前的数据统计
    log_progress(f"过滤前数据统计:")
    log_progress(f"  总记录数: {len(final_df)}")
    log_progress(f"  open列非空数: {final_df['open'].notna().sum()}")
    log_progress(f"  high列非空数: {final_df['high'].notna().sum()}")
    log_progress(f"  low列非空数: {final_df['low'].notna().sum()}")
    log_progress(f"  close列非空数: {final_df['close'].notna().sum()}")
    log_progress(f"  vol列非空数: {final_df['vol'].notna().sum()}")

    final_df.dropna(subset=['open', 'high', 'low', 'close', 'vol'], how='all', inplace=True)
    log_progress(f"过滤后数据形状: {final_df.shape}")

    if not final_df.empty:
        log_progress(f"最终数据样本:")
        log_progress(f"{final_df.head().to_string()}")
    else:
        log_progress("最终数据为空!")

    total_saved_records = save_data_to_db(final_df, 'ts_stock_daily')

    log_progress("===== 处理完成 =====")
    log_progress(f"总共处理了 {len(stocks_df)} 只股票")
    log_progress(f"成功保存了 {total_saved_records} 条记录到数据库")


if __name__ == "__main__":
    warnings.simplefilter(action='ignore', category=FutureWarning)
    
    if start_wind_api():
        try:
            process_all_stocks()
        finally:
            stop_wind_api()
